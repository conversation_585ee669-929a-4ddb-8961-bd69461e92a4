using System.Diagnostics;
using System.Runtime.InteropServices;
using AvrStream.Base.Streamer.Pipes;
using AvrStream.Gateway.Entities.Models;
using Gst;
using Microsoft.Extensions.Logging;
using DateTime = System.DateTime;
using MessageType = Gst.MessageType;
using Task = System.Threading.Tasks.Task;
using Uri = System.Uri;

namespace AvrStream.Gateway.Streamer.Pipes;

using Uri = Uri;

// gst-launch-1.0  v4l2src device=/dev/video0 io-mode=4 name=vid_src do-timestamp=True ! image/jpeg, width=1920, height=1080, framerate=30/1 ! jpegdec ! videoflip video-direction=identity ! clockoverlay halignment=2 valignment=2 font-desc="Verdana Medium 9" time-format="%d/%m/%Y %H:%M:%S" ! videoconvert ! nvh264enc ! h264parse config-interval=-1 ! tee name=cam_tee
// cam_tee. ! queue leaky=downstream silent=true flush-on-eos=true ! muxer.video
// cam_tee. ! queue leaky=downstream silent=true flush-on-eos=true ! s_local.
// cam_tee. ! queue leaky=downstream silent=true flush-on-eos=true ! s_server.
// alsasrc device=front:3 name=mic_src do-timestamp=True ! queue leaky=downstream silent=true flush-on-eos=true ! webrtcdsp echo-cancel=false ! audioconvert ! audioresample ! level name=level message=true ! queue leaky=downstream silent=true flush-on-eos=true ! opusenc ! opusparse ! tee name=mic_tee
// mic_tee. ! queue leaky=downstream silent=true flush-on-eos=true ! muxer.audio_0
// mic_tee. ! queue leaky=downstream silent=true flush-on-eos=true ! s_local. mic_tee. ! queue leaky=downstream silent=true flush-on-eos=true ! s_server.
// splitmuxsink name=muxer location="/home/<USER>/record/data/2024-10-28/%05d.mp4"      max-size-time=1000000000000 max-size-bytes=314572800 use-robust-muxing=true async-finalize=true start-index=5 sink=filesink muxer-properties="properties,streamable=true,reserved-max-duration=1060000000000,reserved-moov-update-period=5000000000"
// rtspclientsink name=s_local location=rtsp://localhost:8554/stream
// rtspclientsink name=s_server location=rtsp://*************:8554/stream
// direction
// (0): identity         - GST_VIDEO_ORIENTATION_IDENTITY
// (1): 90r              - GST_VIDEO_ORIENTATION_90R
// (2): 180              - GST_VIDEO_ORIENTATION_180
// (3): 90l              - GST_VIDEO_ORIENTATION_90L
public class RecordPipe : BasePipe
{
    private static readonly Func<RecordSetting, string> CamFormat =
        (recordSetting) => @$"
{recordSetting.Cam.GetGstInput($"vid_src", true)} ! {recordSetting.Cap.GetGstCapInput()} ! videoflip video-direction={recordSetting.Direction} ! clockoverlay halignment=2 valignment=2 font-desc=""Verdana Medium 9"" time-format=""%d/%m/%Y %H:%M:%S"" ! 
{recordSetting.GstVideoEncodeElement} name=vid_enc bitrate={(RuntimeInformation.RuntimeIdentifier.Contains("ubuntu.18.04-arm64") ? recordSetting.CameraRecordSetting.BitRate : recordSetting.CameraRecordSetting.BitRate / 1000)} ! h264parse config-interval=-1 ! tee name=cam_tee
cam_tee. ! queue leaky=downstream silent=true flush-on-eos=true ! muxer.video
cam_tee. ! queue leaky=downstream silent=true flush-on-eos=true ! s_local.
{(recordSetting.OnLine ? "cam_tee. ! queue leaky=downstream silent=true flush-on-eos=true ! s_server." : "")}
";

    private static readonly Func<RecordSetting, SystemSetting, string> MicFormat =
        (recordSetting, systemSetting) =>
            @$"
{recordSetting.Mic.GetGstInput($"mic_src", true)} ! webrtcechoprobe name=webrtcechoprobe0 ! queue leaky=downstream silent=true flush-on-eos=true ! webrtcdsp name=webrtcdsp_mic 
{(systemSetting.IsEchoProcessing ? $"echo-cancel=true echo-suppression-level={systemSetting.EchoProcessingLevel}" : "echo-cancel=false")} {(systemSetting.IsNoiseProcessing ? $"noise-suppression=true noise-suppression-level={systemSetting.NoiseProcessingLevel}" : "noise-suppression=false")} ! audioconvert ! audioresample ! level name=level message=true ! tee name=mic_tee 
mic_tee. ! queue leaky=downstream flush-on-eos=true ! {recordSetting.GstAudioEncodeElementMp4} ! queue leaky=downstream silent=true flush-on-eos=true ! muxer.audio_0 
mic_tee. ! queue leaky=downstream flush-on-eos=true ! {recordSetting.GstAudioEncodeElementStream} ! queue leaky=downstream silent=true flush-on-eos=true ! s_local. 
{(recordSetting.OnLine ? "mic_tee. ! queue leaky=downstream silent=true flush-on-eos=true ! s_server." : "")}";

    private static readonly Func<string, RecordSetting, SystemSetting, string> MuxFormat =
        (path, recordSetting, systemSetting) => @$"
    splitmuxsink name=muxer
    muxer=qtmux 
    location=""{path}/{DateTime.Now:HHmmss}_{recordSetting.RtspPart.Replace("/", "_")}_%05d.mp4"" 
    max-size-time={recordSetting.SplitAfterSeconds * GST_SECOND}
    max-size-bytes={systemSetting.SplitAfterMb * 1024L * 1024L}
    use-robust-muxing=true
    async-finalize=true
    start-index=0
    sink=filesink 
    muxer-properties=""properties,streamable=true,reserved-max-duration={recordSetting.SplitAfterSeconds * GST_SECOND + 60 * GST_SECOND},reserved-moov-update-period={5 * GST_SECOND}""
";

    private static readonly Func<RecordSetting, string, string> StreamFormat =
        (recordSetting, server) =>
            @$" rtspclientsink name=s_local location=rtsp://localhost:8554/{recordSetting.RtspPart} 
{(RuntimeInformation.RuntimeIdentifier.Contains("ubuntu.18.04-arm64") ? "sync=false" : "")} 
{(recordSetting.OnLine ? $"rtspclientsink name=s_server location=rtsp://{server}:8554/{recordSetting.RtspPart} {(RuntimeInformation.RuntimeIdentifier.Contains("ubuntu.18.04-arm64") ? "sync=false" : "")}" : "")}";

    private bool _stopping;
    private bool _eosRaise;
    private System.Timers.Timer _timer;

    public RecordPipe(ILoggerFactory loggerFactory, SystemSetting systemSetting,
        RecordSetting recordSetting,
        Action<bool, bool> stateChangedHandler = null,
        Action<string> fileAddedHandler = null,
        Action eosHandler = null,
        Action<string> errorHandler = null) :
        base(loggerFactory)
    {
        var st = Stopwatch.StartNew();
        var desc = BuildPipelineDesc(systemSetting, recordSetting).Replace("\r\n", " ").Replace("\n", " ");
        Logger.LogInformation($"RECORD: gst-launch-1.0 {desc}");

        Instance = (Pipeline)Parse.Launch(desc);

        Instance.Bus.AddWatch((_, message) =>
        {
            switch (message.Type)
            {
                case MessageType.Element:
                    var structure = message.Structure;
                    if (structure == null) break;
                    var name = structure.Name;

                    if (string.Equals(name, "splitmuxsink-fragment-closed"))
                    {
                        var file = structure.GetString("location");

                        Logger.LogInformation($"New file added: {file}");
                        fileAddedHandler?.Invoke(file);

                        if (_stopping) Logger.LogInformation("Last file added, waiting EOS...");
                    }

                    break;
                case MessageType.StateChanged:
                    if (message.Src == Instance)
                    {
                        message.ParseStateChanged(out var oldState, out var newState, out var pending);

                        var isLoading = oldState == State.Null || oldState == State.Ready;
                        var isPlaying = pending == State.Playing ||
                                        (pending == State.VoidPending && newState == State.Playing);
                        var isChanging = pending != State.VoidPending;

                        stateChangedHandler?.Invoke(isPlaying, isChanging);

                        Logger.LogInformation(
                            $"STATE CHANGED: from {oldState.ToString()} to {newState.ToString()} (loading={isLoading}, pending={pending.ToString()})");
                    }

                    break;
                case MessageType.Error:
                    message.ParseError(out var err, out var debug);
                    Logger.LogError($"msg={err?.Message}, debug={debug}");
                    errorHandler?.Invoke($"{err?.Message}");
                    break;
                case MessageType.Eos:
                    Logger.LogInformation("Got EOS, disposing...");
                    _eosRaise = true;
                    eosHandler?.Invoke();
                    break;
            }

            return true;
        });
        Logger.LogInformation($"Setup record pipeline in {st.ElapsedMilliseconds}ms");

        st.Restart();
        Instance.SetState(State.Playing);
        Logger.LogInformation($"Set record pipeline to PLAYING in {st.ElapsedMilliseconds}ms");
    }

    private static string BuildPipelineDesc(SystemSetting systemSetting, RecordSetting recordSetting)
    {
        var videoDesc = "";
        if (recordSetting.Cam != null)
            videoDesc = CamFormat(recordSetting);

        var audioDesc = "";
        if (recordSetting.Mic != null)
            audioDesc = MicFormat(recordSetting, systemSetting);

        var path = new Uri(recordSetting.Path).AbsolutePath;
        var splitSinkDesc = MuxFormat(path, recordSetting, systemSetting);
        string server;
        Uri.TryCreate(systemSetting.ServerUrl, UriKind.Absolute, out var uri);
        {
            server = uri!.Host;
        }
        var streamDecs =
            StreamFormat(recordSetting, server);

        var desc = videoDesc + audioDesc + splitSinkDesc + streamDecs;
        return desc;
    }

    public void Stop()
    {
        _stopping = true;
        _eosRaise = false;
        var st = Stopwatch.StartNew();

        try
        {
            Logger.LogInformation("Stopping record pipeline by sending EOS...");
            Instance.SendEvent(Event.NewEos());

            // Use timeout with more reasonable duration (30s instead of 300s)
            _timer = new System.Timers.Timer(30000);
            _timer.Elapsed += (sender, e) =>
            {
                Logger.LogWarning("EOS not raised in 30s, force stopping pipeline...");
                if (!_eosRaise)
                {
                    try
                    {
                        Instance.SetState(State.Paused);
                        Thread.Sleep(50);
                        Instance.SetState(State.Ready);
                        _eosRaise = true;
                    }
                    catch (Exception ex)
                    {
                        Logger.LogError(ex, "Error during force stop");
                        _eosRaise = true; // Set to true to break the loop
                    }
                }
            };

            _timer.Start();

            // Use more efficient waiting with timeout
            var timeout = TimeSpan.FromSeconds(35); // Slightly longer than timer
            var startTime = DateTime.UtcNow;

            while (!_eosRaise && (DateTime.UtcNow - startTime) < timeout)
            {
                Thread.Sleep(100);
            }

            if (!_eosRaise)
            {
                Logger.LogError("Pipeline stop operation timed out completely");
            }
        }
        catch (Exception e)
        {
            Logger.LogError(e, "Error during pipeline stop operation");
        }
        finally
        {
            try
            {
                _timer?.Stop();
                _timer?.Dispose();
                _timer = null;

                Dispose();
                Logger.LogInformation($"Record pipeline stopped in {st.ElapsedMilliseconds}ms");
            }
            catch (Exception e)
            {
                Logger.LogError(e, "Error during pipeline cleanup");
            }
            finally
            {
                st.Stop();
            }
        }
    }

    public async Task<bool> StopAsync(CancellationToken cancellationToken = default)
    {
        if (_stopping) return true; // Already stopping

        _stopping = true;
        _eosRaise = false;
        var st = Stopwatch.StartNew();

        try
        {
            Logger.LogInformation("Stopping record pipeline asynchronously by sending EOS...");
            Instance.SendEvent(Event.NewEos());

            // Wait for EOS with cancellation support
            var timeout = TimeSpan.FromSeconds(30);
            var startTime = DateTime.UtcNow;

            while (!_eosRaise &&
                   (DateTime.UtcNow - startTime) < timeout &&
                   !cancellationToken.IsCancellationRequested)
            {
                await Task.Delay(100, cancellationToken);
            }

            if (cancellationToken.IsCancellationRequested)
            {
                Logger.LogWarning("Pipeline stop operation was cancelled");
                return false;
            }

            if (!_eosRaise)
            {
                Logger.LogWarning("EOS not raised in time, force stopping pipeline...");
                try
                {
                    Instance.SetState(State.Paused);
                    await Task.Delay(50, cancellationToken);
                    Instance.SetState(State.Ready);
                    _eosRaise = true;
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, "Error during force stop");
                    return false;
                }
            }

            Dispose();
            Logger.LogInformation($"Record pipeline stopped asynchronously in {st.ElapsedMilliseconds}ms");
            return true;
        }
        catch (Exception e)
        {
            Logger.LogError(e, "Error during async pipeline stop operation");
            return false;
        }
        finally
        {
            st.Stop();
        }
    }

    public void UpdateBitrate(int newBitrate)
    {
        var videoEncoder = Instance.GetByName("vid_enc");
        if (videoEncoder != null)
        {
            // in jetson nano bitrate is in bps, else is in kbps
            videoEncoder.SetProperty("bitrate",
                RuntimeInformation.RuntimeIdentifier.Contains("ubuntu.18.04-arm64")
                    ? new GLib.Value(newBitrate)
                    : new GLib.Value(newBitrate / 1000));

            Logger.LogInformation($"Updated bitrate to {newBitrate}");
        }
        else
        {
            Logger.LogWarning("Video encoder element not found.");
        }
    }

    public void UpdateNoiseProcessing(bool isNoiseProcessing, int? noiseProcessingLevel)
    {
        var micProcessing = Instance.GetByName("webrtcdsp_mic");
        if (micProcessing != null)
        {
            if (isNoiseProcessing)
            {
                micProcessing.SetProperty("noise-suppression", new GLib.Value(true));
                micProcessing.SetProperty("noise-suppression-level", new GLib.Value(noiseProcessingLevel));
            }
            else
            {
                micProcessing.SetProperty("noise-suppression", new GLib.Value(false));
            }

            Logger.LogInformation($"Updated noise processing to {isNoiseProcessing} with level {noiseProcessingLevel}");
        }
        else
        {
            Logger.LogWarning("Mic webrtcdsp element not found.");
        }
    }

    public void UpdateEchoProcessing(bool isEchoProcessing, int? echoProcessingLevel)
    {
        var micProcessing = Instance.GetByName("webrtcdsp_mic");
        if (micProcessing != null)
        {
            if (isEchoProcessing)
            {
                micProcessing.SetProperty("echo-cancel", new GLib.Value(true));
                micProcessing.SetProperty("echo-suppression-level", new GLib.Value(echoProcessingLevel));
            }
            else
            {
                micProcessing.SetProperty("echo-cancel", new GLib.Value(false));
            }

            Logger.LogInformation(
                $"Updated echo processing to {isEchoProcessing} with level {echoProcessingLevel}");
        }
        else
        {
            Logger.LogWarning("Mic webrtcdsp element not found.");
        }
    }
}