using AvrStream.Base.Streamer.Devices;
using AvrStream.Gateway.Entities.Models;

namespace AvrStream.Gateway.Streamer;

public class RecordSetting
{
    public string Path { get; set; } = "";
    public CameraRecordSetting CameraRecordSetting { get; set; }
    public bool OnLine { get; set; }
    public string GstVideoEncodeElement { get; set; } = "";
    public string GstAudioEncodeElementMp4 { get; set; } = "";
    public string GstAudioEncodeElementStream { get; set; } = "";
    public int Direction { get; set; } = 0;
    public int SplitAfterSeconds { get; set; } = 3600;
    public string UserName { get; set; } = "";
    public string Password { get; set; } = "";
    public string RtspPart { get; set; } = "";
    public AvrCam Cam { get; set; }
    public AvrVideoCap Cap { get; set; }
    public AvrMic Mic { get; set; }
}