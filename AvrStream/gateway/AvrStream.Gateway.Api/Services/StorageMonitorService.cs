using AvrStream.Gateway.Api.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace AvrStream.Gateway.Api.Services
{
    public class StorageMonitorService
    {
        private readonly ILogger<StorageMonitorService> _logger;
        private readonly IConfiguration _configuration;
        private readonly ResourceService _resourceService;
        private readonly RecordService _recordService;
        private readonly MessageService _messageService;
        private bool _recordingStopped = false;

        public StorageMonitorService(
            ILogger<StorageMonitorService> logger,
            IConfiguration configuration,
            ResourceService resourceService,
            RecordService recordService,
            MessageService messageService)
        {
            _logger = logger;
            _configuration = configuration;
            _resourceService = resourceService;
            _recordService = recordService;
            _messageService = messageService;
        }

        public async Task<bool> CheckStorageAndStopIfNeeded()
        {
            try
            {
                var availableGb = _configuration.GetValue<long?>("Cleanup:KeepAvailableGb");
                var availablePercentage = _configuration.GetValue<int?>("Cleanup:KeepAvailablePercentage");
                var dataPath = _configuration.GetValue<string>("DataPath");

                if (availableGb == null && availablePercentage == null)
                    return false;

                var (total, available) = _resourceService.GetStorageInfo(dataPath);
                
                bool shouldStop = false;
                string reason = "";

                if (availableGb > 0 && available < availableGb * 1024 * 1024 * 1024)
                {
                    shouldStop = true;
                    reason = $"Available storage ({available / (1024 * 1024 * 1024)}GB) below threshold ({availableGb}GB)";
                }
                else if (availablePercentage > 0 && available * 100.0 / total < availablePercentage)
                {
                    shouldStop = true;
                    var currentPercentage = Math.Round(available * 100.0 / total, 2);
                    reason = $"Available storage ({currentPercentage}%) below threshold ({availablePercentage}%)";
                }

                if (shouldStop && !_recordingStopped)
                {
                    _logger.LogWarning("Storage threshold exceeded. Stopping all recordings. {Reason}", reason);
                    
                    await _recordService.StopAllAsync();
                    _recordingStopped = true;
                    
                    _messageService.CreateMessage(
                        $"CRITICAL: All recordings stopped due to low storage. {reason}",
                        Entities.Models.MessageType.Error);
                    
                    return true;
                }
                else if (!shouldStop && _recordingStopped)
                {
                    _logger.LogInformation("Storage threshold no longer exceeded. Recordings can be resumed.");
                    _recordingStopped = false;
                    
                    _messageService.CreateMessage(
                        "Storage space recovered. Recordings can be resumed manually.",
                        Entities.Models.MessageType.Info);
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking storage threshold");
                return false;
            }
        }

        public bool IsRecordingStoppedDueToStorage() => _recordingStopped;
    }
}