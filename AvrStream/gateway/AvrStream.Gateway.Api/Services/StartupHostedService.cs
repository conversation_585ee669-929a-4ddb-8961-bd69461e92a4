using System.Net;
using System.Net.NetworkInformation;
using System.Net.Sockets;
using System.Text.RegularExpressions;
using AvrStream.Gateway.Api.Models.Requests;
using AvrStream.Gateway.Api.Utils;
using AvrStream.Gateway.Entities.Databases;
using AvrStream.Gateway.Entities.Models;
using GLib;
using Gst;
using Application = Gst.Application;
using MessageType = Gst.MessageType;
using Task = System.Threading.Tasks.Task;
using Thread = System.Threading.Thread;

namespace AvrStream.Gateway.Api.Services;

public class StartupHostedService : IHostedService
{
    private readonly ILogger<StartupHostedService> _logger;
    private readonly IConfiguration _configuration;
    private readonly RecordService _recordService;
    private readonly SystemConfigurationService _systemConfigurationService;
    private readonly MessageService _messageService;
    private readonly ClientService _clientService;
    private readonly IServiceScopeFactory _factory;
    private readonly RecordingFileService _recordingFileService;
    public static DeviceMonitor DeviceMonitor;

    private static readonly MainLoop MainLoop = new();

    public StartupHostedService(ILogger<StartupHostedService> logger, IConfiguration configuration,
        IServiceScopeFactory factory, SystemConfigurationService systemConfigurationService,
        RecordService recordService, ClientService clientService, MessageService messageService,
        RecordingFileService recordingFileService)
    {
        _factory = factory;
        _logger = logger;
        _configuration = configuration;
        _recordService = recordService;
        _systemConfigurationService = systemConfigurationService;
        _messageService = messageService;
        _clientService = clientService;
        _recordingFileService = recordingFileService;
    }


    public async Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("StartupHostedService started");
        Init();
        var systemSettingDto = await _systemConfigurationService.GetSystemSettingAsync();
        var systemSetting = new SystemSetting
        {
            Name = systemSettingDto.Name,
            IsEncrypted = systemSettingDto.IsEncrypted,
            Password = systemSettingDto.Password,
            Iv = systemSettingDto.Iv,
            ServerUrl = systemSettingDto.ServerUrl,
            SplitAfterMb = systemSettingDto.SplitAfterMb,
            MaxFileLengthSeconds = systemSettingDto.MaxFileLengthSeconds,
            RecordPath = systemSettingDto.RecordPath,
            StreamUrl1 = systemSettingDto.StreamUrl1,
            StreamUrl2 = systemSettingDto.StreamUrl2,
            IsEchoProcessing = systemSettingDto.IsEchoProcessing,
            EchoProcessingLevel = systemSettingDto.EchoProcessingLevel,
            IsNoiseProcessing = systemSettingDto.IsNoiseProcessing,
            NoiseProcessingLevel = systemSettingDto.NoiseProcessingLevel
        };
        await CheckFileMissed(systemSetting);
        await CheckMp4FilesIntegrity(systemSetting);
        var isRegister = await _clientService.CheckBoxRegisterAsync();
        if (!isRegister.Success)
        {
            var name = systemSetting.Name;
            if (string.IsNullOrWhiteSpace(systemSetting.Name))
            {
                name = _configuration.GetValue<string>("SystemSetting:Name");
            }

            var ips = NetworkInterface
                .GetAllNetworkInterfaces()
                .Where(t => (t.NetworkInterfaceType == NetworkInterfaceType.Ethernet ||
                             t.NetworkInterfaceType == NetworkInterfaceType.Wireless80211)
                            && t.OperationalStatus == OperationalStatus.Up)
                .SelectMany(t => t.GetIPProperties().UnicastAddresses)
                .Where(t => t.Address.AddressFamily == AddressFamily.InterNetwork &&
                            !IPAddress.IsLoopback(t.Address))
                .Select(t => t.Address)
                .ToList();
            var ip = ips.FirstOrDefault()?.ToString();
            var file = _configuration.GetValue<string>("UniqueFilePath");
            var uniqueId = await File.ReadAllTextAsync(file, cancellationToken);
            var pattern = @"[^a-zA-Z0-9_-]";
            uniqueId = Regex.Replace(uniqueId, pattern, "");
            var response = await _clientService.CreateBoxAsync(new CreateOrUpdateBoxRequest
            {
                Name = name,
                IpAddress = ip,
                UniqueId = uniqueId,
                UrlStream1 = systemSetting.StreamUrl1,
                UrlStream2 = systemSetting.StreamUrl2
            });
            if (!response.Success)
            {
                _logger.LogError("Failed to create box.");
            }
        }

        var res = await _recordService.Init(systemSetting);
        _logger.LogInformation($"RecordService init: {res}");
        await _recordService.StartAll();
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        _recordService.StopAll();
        if (MainLoop.IsRunning)
            MainLoop.Quit();
        _logger.LogInformation("StartupHostedService stopped");
        return Task.CompletedTask;
    }

    private void Init()
    {
        Application.Init();
        new Thread(MainLoop.Run).Start();
        DeviceMonitor = new DeviceMonitor();
        DeviceMonitor.AddFilter("Video/Source", null);
        DeviceMonitor.AddFilter("Source/Video", null);
        DeviceMonitor.AddFilter("Audio/Source", null);
        DeviceMonitor.AddFilter("Source/Audio", null);
        DeviceMonitor.Start();
        while (DeviceMonitor.Bus.HavePending()) DeviceMonitor.Bus.Pop();

        /* tracking devices changed */
        DeviceMonitor.Bus.AddWatch((_, message) =>
        {
            switch (message.Type)
            {
                case MessageType.DeviceAdded:
                    _logger.LogInformation($"Device event: {message.Type}");
                    _messageService.CreateMessage("Device added", Entities.Models.MessageType.Alert);
                    break;
                case MessageType.DeviceChanged:
                case MessageType.DeviceRemoved:

                    _logger.LogInformation($"Device event: {message.Type}");
                    _messageService.CreateMessage("Device has changed", Entities.Models.MessageType.Alert);
                    // TODO: reload pipeline

                    break;
                default:
                    _logger.LogInformation($"DeviceMonitor got message: {message.Type}, src={message.Src.Name}");
                    break;
            }

            return true;
        });
    }

    private async Task CheckFileMissed(SystemSetting systemSetting)
    {
        try
        {
            var rootPath = systemSetting.RecordPath;
            var path = Utilities.GetDataPath(rootPath);
            var files = Directory.GetFiles(path, "*.*", SearchOption.AllDirectories);
            var recordingFiles = await _recordingFileService.ListRecordingFilesAsync(new ListRecordingFileRequest
            {
                IsTakeAll = true
            });
            var recordingFileNames = recordingFiles.Data.Select(f => f.Path).ToList();
            var missingFiles = files.Except(recordingFileNames, StringComparer.OrdinalIgnoreCase).ToList();
            foreach (var file in missingFiles)
            {
                var fileName = Path.GetFileName(file);
                var result = _recordingFileService.CreateOrUpdateRecordingFile(new CreateOrUpdateRecordingFileRequest
                {
                    Name = fileName,
                    Path = file,
                    IsEndRecordingFile = true,
                    IsSynchronized = false,
                    EncryptDone = false,
                    IsEncrypt = systemSetting.IsEncrypted,
                    Password = systemSetting.Password,
                    Iv = systemSetting.Iv
                });
                if (result.Success)
                {
                    _logger.LogInformation($"Recording missing file {fileName} is added");
                    _messageService.CreateMessage($"Missing file {fileName} has been recovered", Entities.Models.MessageType.Info);
                }
                else
                {
                    _logger.LogError($"Recording missing file {fileName} is error");
                    _messageService.CreateMessage($"Missing file {fileName} error: {result.Message}", Entities.Models.MessageType.Error);
                }
            }
        }
        catch (Exception e)
        {
            _logger.LogError(e, null);
        }
    }

    /// <summary>
    /// Searches for MP4 files that have not been added to the database and adds them to ensure data integrity.
    /// This method specifically targets MP4 video files to handle cases where files exist on disk but are missing from the database
    /// due to power loss or pipeline issues.
    /// </summary>
    /// <param name="systemSetting">System configuration containing record path and encryption settings</param>
    private async Task CheckMp4FilesIntegrity(SystemSetting systemSetting)
    {
        try
        {
            _logger.LogInformation("Starting MP4 files integrity check...");

            var rootPath = systemSetting.RecordPath;
            var dataPath = Utilities.GetDataPath(rootPath);

            if (!Directory.Exists(dataPath))
            {
                _logger.LogWarning($"Data path does not exist: {dataPath}");
                return;
            }

            // Search specifically for MP4 files
            var mp4Files = Directory.GetFiles(dataPath, "*.mp4", SearchOption.AllDirectories);
            _logger.LogInformation($"Found {mp4Files.Length} MP4 files on disk");

            if (mp4Files.Length == 0)
            {
                _logger.LogInformation("No MP4 files found on disk");
                return;
            }

            // Get all recording files from database
            var recordingFiles = await _recordingFileService.ListRecordingFilesAsync(new ListRecordingFileRequest
            {
                IsTakeAll = true
            });

            var existingFilePaths = recordingFiles.Data.Select(f => f.Path).ToHashSet(StringComparer.OrdinalIgnoreCase);
            _logger.LogInformation($"Found {existingFilePaths.Count} files already in database");

            // Find MP4 files that are not in the database
            var missingMp4Files = mp4Files.Where(file => !existingFilePaths.Contains(file)).ToList();

            if (missingMp4Files.Count == 0)
            {
                _logger.LogInformation("All MP4 files are already registered in the database");
                return;
            }

            _logger.LogInformation($"Found {missingMp4Files.Count} MP4 files missing from database");

            var addedCount = 0;
            var errorCount = 0;

            foreach (var mp4File in missingMp4Files)
            {
                try
                {
                    var fileName = Path.GetFileName(mp4File);
                    var fileInfo = new System.IO.FileInfo(mp4File);

                    // Validate file exists and has content
                    if (!fileInfo.Exists)
                    {
                        _logger.LogWarning($"MP4 file no longer exists: {mp4File}");
                        continue;
                    }

                    if (fileInfo.Length == 0)
                    {
                        _logger.LogWarning($"MP4 file is empty, skipping: {fileName}");
                        continue;
                    }

                    _logger.LogInformation($"Adding missing MP4 file to database: {fileName} (Size: {fileInfo.Length / 1024 / 1024:F2} MB)");

                    var result = _recordingFileService.CreateOrUpdateRecordingFile(new CreateOrUpdateRecordingFileRequest
                    {
                        Name = fileName,
                        Path = mp4File,
                        IsEndRecordingFile = true,
                        IsSynchronized = false,
                        EncryptDone = false,
                        IsEncrypt = systemSetting.IsEncrypted,
                        Password = systemSetting.Password,
                        Iv = systemSetting.Iv
                    });

                    if (result.Success)
                    {
                        addedCount++;
                        _logger.LogInformation($"Successfully added MP4 file to database: {fileName}");
                        _messageService.CreateMessage($"MP4 file recovered and added to database: {fileName}", Entities.Models.MessageType.Info);
                    }
                    else
                    {
                        errorCount++;
                        _logger.LogError($"Failed to add MP4 file to database: {fileName} - {result.Message}");
                        _messageService.CreateMessage($"Failed to recover MP4 file: {fileName} - {result.Message}", Entities.Models.MessageType.Error);
                    }
                }
                catch (Exception fileEx)
                {
                    errorCount++;
                    var fileName = Path.GetFileName(mp4File);
                    _logger.LogError(fileEx, $"Error processing MP4 file: {fileName}");
                    _messageService.CreateMessage($"Error processing MP4 file: {fileName} - {fileEx.Message}", Entities.Models.MessageType.Error);
                }
            }

            // Summary logging
            _logger.LogInformation($"MP4 files integrity check completed. Added: {addedCount}, Errors: {errorCount}, Total processed: {missingMp4Files.Count}");

            if (addedCount > 0)
            {
                _messageService.CreateMessage($"Data integrity check completed: {addedCount} MP4 files recovered and added to database", Entities.Models.MessageType.Info);
            }
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error during MP4 files integrity check");
            _messageService.CreateMessage($"MP4 files integrity check failed: {e.Message}", Entities.Models.MessageType.Error);
        }
    }
}