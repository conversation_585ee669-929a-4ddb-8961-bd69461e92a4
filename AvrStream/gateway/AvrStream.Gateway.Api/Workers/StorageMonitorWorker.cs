using AvrStream.Gateway.Api.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace AvrStream.Gateway.Api.Workers
{
    public class StorageMonitorWorker : BackgroundService
    {
        private readonly ILogger<StorageMonitorWorker> _logger;
        private readonly IServiceScopeFactory _factory;

        public StorageMonitorWorker(ILogger<StorageMonitorWorker> logger, IServiceScopeFactory factory)
        {
            _logger = logger;
            _factory = factory;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("StorageMonitorWorker started");

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    using var scope = _factory.CreateScope();
                    var storageMonitor = scope.ServiceProvider.GetRequiredService<StorageMonitorService>();
                    
                    await storageMonitor.CheckStorageAndStopIfNeeded();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in storage monitoring");
                }

                await Task.Delay(TimeSpan.FromMinutes(2), stoppingToken);
            }
        }

        public override Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("StorageMonitorWorker stopped");
            return base.StopAsync(cancellationToken);
        }
    }
}