{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Jwt": {"Key": "LHPfBVbaQtnwvWhhd5YVaPK4ygVAJbab", "ExpiresInDays": 5, "ExpiresInHours": 4}, "ConnectionStrings": {"VmsUser": "Server=localhost;Port=5432;Database=VmsUser_dev;Username=postgres;Password=yourStrong(!!)Pa$$word;", "Vms": "Server=localhost;Port=5432;Database=Vms_dev;Username=postgres;Password=yourStrong(!!)Pa$$word;"}, "ClientJwt": {"Key": "LHPfBVbaQtnwvWhhd5YVaPK4ygVAJbab", "Issuer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Audience": "ClientAudience"}, "ApiUrl": "http://*************:9000", "Serilog": {"Using": ["Serilog.Sinks.File", "Serilog.Sinks.Console"], "MinimumLevel": "Information", "WriteTo": [{"Name": "File", "Args": {"path": "/mnt/ssd/Logs/VmsApi-log-.txt", "fileSizeLimitBytes": "20971520", "rollOnFileSizeLimit": true, "rollingInterval": "Day", "retainedFileCountLimit": 30}}, {"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}"}}]}}